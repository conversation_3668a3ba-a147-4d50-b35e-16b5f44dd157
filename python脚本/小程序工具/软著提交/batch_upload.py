#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量软著图片上传脚本
可以一次上传多张图片，每张图片使用不同的应用ID
"""

import os
import random
import requests
import time
from pathlib import Path
from config import API_CONFIG, DEFAULT_PARAMS, IMAGE_DIR, SUPPORTED_IMAGE_FORMATS


def get_app_ids():
    """从appid.txt文件中读取所有应用ID"""
    appid_file = "appid.txt"
    if not os.path.exists(appid_file):
        print(f"❌ 文件 {appid_file} 不存在")
        return []
    
    try:
        with open(appid_file, 'r', encoding='utf-8') as f:
            app_ids = [line.strip() for line in f if line.strip()]
        
        if not app_ids:
            print(f"❌ 文件 {appid_file} 中没有找到应用ID")
            return []
        
        print(f"📋 从文件中读取到 {len(app_ids)} 个应用ID")
        return app_ids
    except Exception as e:
        print(f"❌ 读取文件 {appid_file} 失败: {e}")
        return []


def get_all_images():
    """获取所有图片文件"""
    if not os.path.exists(IMAGE_DIR):
        print(f"❌ 目录 {IMAGE_DIR} 不存在")
        return []
    
    image_files = []
    for file_path in Path(IMAGE_DIR).iterdir():
        if file_path.is_file() and file_path.suffix.lower() in SUPPORTED_IMAGE_FORMATS:
            image_files.append(str(file_path))
    
    if not image_files:
        print(f"❌ 在目录 {IMAGE_DIR} 中没有找到图片文件")
        return []
    
    print(f"📸 找到 {len(image_files)} 张图片")
    return image_files


def upload_image(image_path, app_id):
    """上传单张图片"""
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Authorization': API_CONFIG['authorization'],
        'Origin': 'http://game.raisedsun.com',
        'Referer': 'http://game.raisedsun.com/ad/account',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'
    }
    
    cookies = {'Admin-Token': API_CONFIG['admin_token']}
    
    try:
        with open(image_path, 'rb') as f:
            files = {'softFile': (os.path.basename(image_path), f, 'image/png')}
            data = {
                'accountId': DEFAULT_PARAMS['account_id'],
                'appIdListStr': app_id + ','
            }
            
            response = requests.post(
                API_CONFIG['url'],
                headers=headers,
                cookies=cookies,
                files=files,
                data=data,
                verify=False
            )
            
            return {
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'response': response.text
            }
            
    except Exception as e:
        return {'success': False, 'error': str(e)}


def batch_upload(count=None, delay=1):
    """
    批量上传图片
    
    Args:
        count (int): 要上传的图片数量，None表示上传所有图片
        delay (int): 每次上传之间的延迟时间（秒）
    """
    print("🚀 开始批量软著图片上传...")
    
    # 获取应用ID列表
    app_ids = get_app_ids()
    if not app_ids:
        return
    
    # 获取图片列表
    image_files = get_all_images()
    if not image_files:
        return
    
    # 确定上传数量
    if count is None:
        count = len(image_files)
    else:
        count = min(count, len(image_files))
    
    print(f"📊 计划上传 {count} 张图片")
    
    # 随机选择图片
    selected_images = random.sample(image_files, count)
    
    success_count = 0
    fail_count = 0
    
    for i, image_path in enumerate(selected_images, 1):
        # 随机选择应用ID
        app_id = random.choice(app_ids)
        
        print(f"\n📷 [{i}/{count}] 上传图片: {os.path.basename(image_path)}")
        print(f"🎯 使用应用ID: {app_id}")
        
        # 上传图片
        result = upload_image(image_path, app_id)
        
        if result['success']:
            print(f"✅ 上传成功! 响应: {result['response']}")
            success_count += 1
        else:
            print(f"❌ 上传失败!")
            if 'error' in result:
                print(f"   错误: {result['error']}")
            else:
                print(f"   状态码: {result['status_code']}")
                print(f"   响应: {result['response']}")
            fail_count += 1
        
        # 延迟
        if i < count and delay > 0:
            print(f"⏳ 等待 {delay} 秒...")
            time.sleep(delay)
    
    print(f"\n📈 批量上传完成!")
    print(f"✅ 成功: {success_count} 张")
    print(f"❌ 失败: {fail_count} 张")
    print(f"📊 成功率: {success_count/(success_count+fail_count)*100:.1f}%")


def main():
    """主函数"""
    import sys
    
    # 解析命令行参数
    count = None
    delay = 1
    
    if len(sys.argv) > 1:
        try:
            count = int(sys.argv[1])
        except ValueError:
            print("❌ 参数错误: 上传数量必须是整数")
            return
    
    if len(sys.argv) > 2:
        try:
            delay = int(sys.argv[2])
        except ValueError:
            print("❌ 参数错误: 延迟时间必须是整数")
            return
    
    batch_upload(count, delay)


if __name__ == "__main__":
    main()
