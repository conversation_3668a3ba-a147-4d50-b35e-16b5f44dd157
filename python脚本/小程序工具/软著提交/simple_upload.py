#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版软著图片上传脚本
"""

import os
import random
import requests
from pathlib import Path
from config import API_CONFIG, DEFAULT_PARAMS, IMAGE_DIR, SUPPORTED_IMAGE_FORMATS


def get_random_image():
    """随机选择一张图片"""
    if not os.path.exists(IMAGE_DIR):
        print(f"❌ 目录 {IMAGE_DIR} 不存在")
        return None
    
    # 获取所有图片文件
    image_files = []
    for file_path in Path(IMAGE_DIR).iterdir():
        if file_path.is_file() and file_path.suffix.lower() in SUPPORTED_IMAGE_FORMATS:
            image_files.append(str(file_path))
    
    if not image_files:
        print(f"❌ 在目录 {IMAGE_DIR} 中没有找到图片文件")
        return None
    
    selected = random.choice(image_files)
    print(f"📸 随机选择图片: {os.path.basename(selected)}")
    return selected


def upload_image(image_path):
    """上传图片"""
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Authorization': API_CONFIG['authorization'],
        'Origin': 'http://game.raisedsun.com',
        'Referer': 'http://game.raisedsun.com/ad/account',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'
    }
    
    cookies = {'Admin-Token': API_CONFIG['admin_token']}
    
    try:
        with open(image_path, 'rb') as f:
            files = {'softFile': (os.path.basename(image_path), f, 'image/png')}
            data = {
                'accountId': DEFAULT_PARAMS['account_id'],
                'appIdListStr': DEFAULT_PARAMS['app_id_list'] + ','
            }
            
            print(f"🚀 正在上传...")
            response = requests.post(
                API_CONFIG['url'],
                headers=headers,
                cookies=cookies,
                files=files,
                data=data,
                verify=False
            )
            
            print(f"📊 状态码: {response.status_code}")
            print(f"📝 响应: {response.text}")
            
            return response.status_code == 200
            
    except Exception as e:
        print(f"❌ 上传失败: {e}")
        return False


def main():
    """主函数"""
    print("🔄 开始软著图片上传...")
    
    # 选择图片
    image_path = get_random_image()
    if not image_path:
        return
    
    # 上传图片
    success = upload_image(image_path)
    
    if success:
        print("✅ 上传成功!")
    else:
        print("❌ 上传失败!")


if __name__ == "__main__":
    main()
