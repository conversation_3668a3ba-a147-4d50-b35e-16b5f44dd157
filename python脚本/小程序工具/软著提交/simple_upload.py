#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
软著图片上传脚本
按顺序处理appid，已处理的记录到"已处理appid.txt"文件中
"""

import os
import random
import requests
from pathlib import Path
from config import API_CONFIG, DEFAULT_PARAMS, IMAGE_DIR, SUPPORTED_IMAGE_FORMATS


def get_processed_app_ids():
    """获取已处理的应用ID列表"""
    processed_file = "已处理appid.txt"
    if not os.path.exists(processed_file):
        return set()

    try:
        with open(processed_file, 'r', encoding='utf-8') as f:
            processed_ids = {line.strip() for line in f if line.strip()}
        return processed_ids
    except Exception as e:
        print(f"❌ 读取已处理文件失败: {e}")
        return set()


def save_processed_app_id(app_id):
    """保存已处理的应用ID"""
    processed_file = "已处理appid.txt"
    try:
        with open(processed_file, 'a', encoding='utf-8') as f:
            f.write(app_id + '\n')
        print(f"📝 已记录处理完成: {app_id}")
    except Exception as e:
        print(f"❌ 保存已处理记录失败: {e}")


def get_next_app_id():
    """获取下一个待处理的应用ID"""
    appid_file = "appid.txt"
    if not os.path.exists(appid_file):
        print(f"❌ 文件 {appid_file} 不存在")
        return None

    try:
        # 读取所有应用ID
        with open(appid_file, 'r', encoding='utf-8') as f:
            all_app_ids = [line.strip() for line in f if line.strip()]

        if not all_app_ids:
            print(f"❌ 文件 {appid_file} 中没有找到应用ID")
            return None

        # 获取已处理的应用ID
        processed_ids = get_processed_app_ids()

        # 找到第一个未处理的应用ID
        for app_id in all_app_ids:
            if app_id not in processed_ids:
                print(f"📋 总共 {len(all_app_ids)} 个应用ID，已处理 {len(processed_ids)} 个")
                print(f"🎯 下一个待处理应用ID: {app_id}")
                return app_id

        print(f"✅ 所有应用ID都已处理完成！总共处理了 {len(processed_ids)} 个")
        return None

    except Exception as e:
        print(f"❌ 读取文件 {appid_file} 失败: {e}")
        return None


def get_random_image():
    """随机选择一张图片"""
    if not os.path.exists(IMAGE_DIR):
        print(f"❌ 目录 {IMAGE_DIR} 不存在")
        return None
    
    # 获取所有图片文件
    image_files = []
    for file_path in Path(IMAGE_DIR).iterdir():
        if file_path.is_file() and file_path.suffix.lower() in SUPPORTED_IMAGE_FORMATS:
            image_files.append(str(file_path))
    
    if not image_files:
        print(f"❌ 在目录 {IMAGE_DIR} 中没有找到图片文件")
        return None
    
    selected = random.choice(image_files)
    print(f"📸 随机选择图片: {os.path.basename(selected)}")
    return selected


def upload_image(image_path, app_id):
    """上传图片"""
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Authorization': API_CONFIG['authorization'],
        'Origin': 'http://game.raisedsun.com',
        'Referer': 'http://game.raisedsun.com/ad/account',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'
    }

    cookies = {'Admin-Token': API_CONFIG['admin_token']}

    try:
        with open(image_path, 'rb') as f:
            files = {'softFile': (os.path.basename(image_path), f, 'image/png')}
            data = {
                'accountId': DEFAULT_PARAMS['account_id'],
                'appIdListStr': app_id + ','
            }

            print(f"🚀 正在上传...")
            print(f"📱 使用应用ID: {app_id}")
            response = requests.post(
                API_CONFIG['url'],
                headers=headers,
                cookies=cookies,
                files=files,
                data=data,
                verify=False
            )

            print(f"📊 状态码: {response.status_code}")
            print(f"📝 响应: {response.text}")

            return response.status_code == 200

    except Exception as e:
        print(f"❌ 上传失败: {e}")
        return False


def main():
    """主函数"""
    print("🔄 开始软著图片上传...")

    # 获取下一个待处理的应用ID
    app_id = get_next_app_id()
    if not app_id:
        return

    # 选择图片
    image_path = get_random_image()
    if not image_path:
        return

    # 上传图片
    success = upload_image(image_path, app_id)

    if success:
        print("✅ 上传成功!")
        # 记录已处理的应用ID
        save_processed_app_id(app_id)
    else:
        print("❌ 上传失败!")


if __name__ == "__main__":
    main()
