#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
软著图片上传脚本
从软著图片目录中随机选择一张图片上传到指定的API接口
"""

import os
import random
import requests
from pathlib import Path


def get_random_image(image_dir):
    """
    从指定目录中随机选择一张图片文件
    
    Args:
        image_dir (str): 图片目录路径
        
    Returns:
        str: 随机选择的图片文件路径，如果没有图片则返回None
    """
    # 支持的图片格式
    image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'}
    
    # 获取目录中的所有图片文件
    image_files = []
    for file_path in Path(image_dir).iterdir():
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            image_files.append(str(file_path))
    
    if not image_files:
        print(f"在目录 {image_dir} 中没有找到图片文件")
        return None
    
    # 随机选择一张图片
    selected_image = random.choice(image_files)
    print(f"随机选择的图片: {selected_image}")
    return selected_image


def upload_software_copyright_image(image_path, account_id="********", app_id_list="wx42abde57f8dbc3b3"):
    """
    上传软著图片到指定的API接口
    
    Args:
        image_path (str): 图片文件路径
        account_id (str): 账户ID
        app_id_list (str): 应用ID列表
        
    Returns:
        dict: API响应结果
    """
    url = "http://game.raisedsun.com/prod-api/ad/account/batch-upload-csc"
    
    # 请求头
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImEwNTUzOTk4LTY0NzctNGQ4Yi04ZTc1LWQ1NTMzOTc0OGFlNyJ9.BcaeJuNmbxwBf5E3DglJ-jaFg-CuUj6_nRXToHQ7DRlAcXdsBIz9JbVufaqolKmVqXN07pOhTKgfoU09Fw2J9w',
        'Origin': 'http://game.raisedsun.com',
        'Referer': 'http://game.raisedsun.com/ad/account',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'
    }
    
    # Cookies
    cookies = {
        'Admin-Token': 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImEwNTUzOTk4LTY0NzctNGQ4Yi04ZTc1LWQ1NTMzOTc0OGFlNyJ9.BcaeJuNmbxwBf5E3DglJ-jaFg-CuUj6_nRXToHQ7DRlAcXdsBIz9JbVufaqolKmVqXN07pOhTKgfoU09Fw2J9w'
    }
    
    try:
        # 准备文件和表单数据
        with open(image_path, 'rb') as f:
            files = {
                'softFile': (os.path.basename(image_path), f, 'image/png')
            }
            
            data = {
                'accountId': account_id,
                'appIdListStr': app_id_list + '，'  # 注意末尾的中文逗号
            }
            
            print(f"正在上传图片: {image_path}")
            print(f"账户ID: {account_id}")
            print(f"应用ID列表: {app_id_list}")
            
            # 发送请求
            response = requests.post(
                url, 
                headers=headers, 
                cookies=cookies, 
                files=files, 
                data=data,
                verify=False  # 对应curl中的--insecure参数
            )
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            return {
                'status_code': response.status_code,
                'response': response.text,
                'success': response.status_code == 200
            }
            
    except FileNotFoundError:
        print(f"错误: 找不到图片文件 {image_path}")
        return {'success': False, 'error': '文件不存在'}
    except Exception as e:
        print(f"上传过程中发生错误: {str(e)}")
        return {'success': False, 'error': str(e)}


def main():
    """主函数"""
    # 软著图片目录
    image_dir = "软著图片"
    
    # 检查目录是否存在
    if not os.path.exists(image_dir):
        print(f"错误: 目录 {image_dir} 不存在")
        return
    
    # 随机选择一张图片
    image_path = get_random_image(image_dir)
    if not image_path:
        return
    
    # 上传图片
    result = upload_software_copyright_image(image_path)
    
    if result['success']:
        print("✅ 图片上传成功!")
    else:
        print("❌ 图片上传失败!")
        if 'error' in result:
            print(f"错误信息: {result['error']}")


if __name__ == "__main__":
    main()
