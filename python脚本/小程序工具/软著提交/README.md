# 软著图片上传工具

这是一个用于自动上传软著图片到指定API接口的Python工具。

## 文件说明

- `simple_upload.py` - 简化版上传脚本（推荐单张上传使用）
- `batch_upload.py` - 批量上传脚本（推荐批量上传使用）
- `upload_software_copyright.py` - 完整版上传脚本
- `config.py` - 配置文件
- `requirements.txt` - Python依赖包
- `appid.txt` - 应用ID列表文件（一行一个应用ID）
- `软著图片/` - 存放软著图片的目录

## 功能特点

- 🎲 **随机选择图片**: 从软著图片目录中随机选择图片进行上传
- 📱 **随机选择应用ID**: 从appid.txt文件中随机选择应用ID，每次上传使用不同ID
- 📸 **多格式支持**: 支持 PNG, JPG, JPEG, GIF, BMP, WEBP 等图片格式
- 🚀 **简单易用**: 一键运行，自动完成上传
- 📦 **批量上传**: 支持一次上传多张图片，每张使用不同应用ID
- ⚙️ **可配置**: 通过配置文件轻松修改参数

## 使用方法

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 准备文件

- 将软著图片放入 `软著图片/` 目录中
- 在 `appid.txt` 文件中添加应用ID，每行一个

### 3. 运行脚本

```bash
# 单张图片上传（推荐）
python3 simple_upload.py

# 批量上传（上传所有图片）
python3 batch_upload.py

# 批量上传指定数量（例如上传5张图片，每次间隔2秒）
python3 batch_upload.py 5 2

# 完整版脚本
python3 upload_software_copyright.py
```

## 配置说明

可以在 `config.py` 文件中修改以下配置：

- `API_CONFIG`: API接口配置（URL、认证信息等）
- `DEFAULT_PARAMS`: 默认参数（账户ID、应用ID等）
- `IMAGE_DIR`: 图片目录路径
- `SUPPORTED_IMAGE_FORMATS`: 支持的图片格式

## 输出示例

```
🔄 开始软著图片上传...
📸 随机选择图片: Snipaste_2025-07-30_11-18-58.png
� 从文件中读取到 135 个应用ID
🎯 随机选择应用ID: wx55e461263e2067b6
�🚀 正在上传...
📱 使用应用ID: wx55e461263e2067b6
📊 状态码: 200
📝 响应: {"status":1,"message":"上传成功"}
✅ 上传成功!
```

## 注意事项

- 确保 `软著图片/` 目录中有图片文件
- 确保 `appid.txt` 文件存在且包含应用ID
- 确保网络连接正常
- 如果认证信息过期，需要更新 `config.py` 中的 token
- 脚本会自动处理SSL证书验证问题

## 故障排除

1. **找不到图片文件**: 检查 `软著图片/` 目录是否存在且包含图片
2. **找不到应用ID**: 检查 `appid.txt` 文件是否存在且包含应用ID
3. **认证失败**: 更新 `config.py` 中的认证信息
4. **网络错误**: 检查网络连接和API地址是否正确
