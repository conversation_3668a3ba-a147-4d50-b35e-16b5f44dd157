软著图片上传工具使用说明
====================

功能说明：
- 按顺序处理appid.txt中的应用ID，每次上传使用一个
- 随机选择软著图片目录中的一张图片进行上传
- 已处理的应用ID自动记录到"已处理appid.txt"文件，避免重复处理

使用方法：
1. 确保appid.txt文件中有应用ID（一行一个）
2. 确保软著图片目录中有图片文件
3. 运行命令：python3 simple_upload.py

文件说明：
- simple_upload.py：主上传脚本
- config.py：配置文件
- appid.txt：应用ID列表
- 已处理appid.txt：已处理的应用ID记录（自动生成）
- 软著图片/：存放图片的目录

注意事项：
- 每次运行只处理一个应用ID
- 如需重新处理所有应用ID，删除"已处理appid.txt"文件即可
- 上传成功后会自动记录已处理的应用ID
